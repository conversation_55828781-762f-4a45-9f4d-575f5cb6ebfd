# HDF5文件可视化与标注工具

这是一个用于HDF5文件可视化和subtask标注的工具，主要功能包括：

1. 自动遍历读取HDF5文件中与图像相关的键，将图像显示为独立的可拖动窗口
2. 通过下拉窗口显示所有键，能够直接显示一维键的值内容
3. 通过进度条控制显示对应帧的图像或其他键的内容，支持播放速率控制
4. 能够选择对应的键，将非零或非false的值在进度条上以不同颜色显示
5. 支持通过鼠标点击进度条上的非零段，添加subtask描述
6. 标注完成的段会显示为完成状态颜色
7. 系统设计解耦，方便后续功能扩展
8. 支持多个数据键的并列显示，每个数据键使用独立的时间轴
9. 支持添加和移除选中的数据键
10. 提供双段滑块范围选择功能，可以直接选择一段帧并添加subtask

## 环境要求

- Python 3.6+
- PyQt5
- h5py
- numpy
- matplotlib
- pillow

## 安装依赖

```bash
pip install -r requirements.txt
```

## 关于中文显示

本程序已优化中文字体显示，采用以下措施确保中文正常显示：

1. 在项目的`fonts`目录中包含了文泉驿微米黑字体，确保在各种环境下都能正确显示中文
2. 程序会自动检测系统中可用的中文字体，优先使用系统中的中文字体
3. 所有源代码和数据都使用UTF-8编码，确保中文处理的一致性

如果仍然遇到中文显示问题，可以尝试：

- 在系统中安装中文字体（如文泉驿、思源黑体等）
- 在程序启动前设置环境变量：`export LC_ALL=zh_CN.UTF-8`
- 运行`test_font.py`脚本测试字体加载情况

## 使用方法

运行主程序：

```bash
python main.py
```

### 基本操作流程：

1. 打开HDF5文件
2. 从左侧列表中选择要显示的图像键，图像将在独立窗口中显示
3. 在左侧列表中选择数据键，数据将在右侧数据区域显示
4. 点击"添加所选键"按钮，将数据键添加到进度条中
5. 如需移除数据键，选中后点击"移除所选键"按钮
6. 通过时间轴进度条控制查看不同帧的内容
7. 点击进度条上的有色段落，可以为该段添加subtask描述
8. 点击"开始段选择"按钮，可以使用范围选择器选择一段帧
9. 在范围选择模式下，可以拖动范围的两端或整个范围
10. 选择范围后按回车键或点击其他区域，将弹出对话框输入subtask描述
11. 可以通过FPS控制和播放按钮控制自动播放速度

## 项目结构

```
.
├── main.py                # 主程序入口
├── requirements.txt       # 依赖列表
├── README.md              # 项目说明文档
├── fonts/                 # 字体文件目录
└── src/                   # 源代码目录
    ├── core/              # 核心功能模块
    │   ├── __init__.py
    │   └── hdf5_model.py  # HDF5数据模型
    ├── ui/                # 用户界面模块
    │   ├── __init__.py
    │   ├── image_window.py    # 图像显示窗口
    │   ├── main_window.py     # 主窗口
    │   └── timeline_widget.py # 时间轴控件
    └── utils/             # 工具类
        ├── __init__.py
        ├── font_helper.py     # 字体帮助工具
        └── hdf5_reader.py     # HDF5读取工具
```

## 扩展与定制

- 可以通过修改`src/ui/`目录下的界面组件来定制界面
- 可以通过扩展`src/core/hdf5_model.py`来添加新的数据处理功能
- 可以通过修改`src/utils/hdf5_reader.py`来扩展文件读取功能
- 如需添加更多字体，可以将字体文件(.ttf/.otf)放入`fonts`目录

## 许可证

MIT 