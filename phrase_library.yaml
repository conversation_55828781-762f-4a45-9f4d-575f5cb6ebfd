动作指令:
  - 向前移动
  - 向后移动
  - 向左转
  - 向右转
  - 停止
  - 加速
  - 减速
  - 抓取物体
  - 放下物体
  - 观察环境
  - 导航到目标
  - 避开障碍物

状态描述:
  - 任务开始
  - 任务进行中
  - 任务完成
  - 等待指令
  - 发生错误
  - 系统正常
  - 需要人工干预
  - 数据收集中
  - 环境检测
  - 位置校准
  - 传感器校准
  - 系统初始化

场景描述:
  - 室内环境
  - 室外环境
  - 光线充足
  - 光线不足
  - 障碍物较多
  - 路径清晰
  - 复杂地形
  - 平坦地面
  - 目标可见
  - 目标遮挡
  - 天气良好
  - 恶劣天气

交互行为:
  - 与人交互
  - 避开障碍
  - 跟随目标
  - 搜索物体
  - 导航路径
  - 学习行为
  - 重复操作
  - 调整策略
  - 记录数据
  - 发送反馈
  - 接收指令
  - 确认任务

机器学习相关:
  - 数据采集
  - 特征提取
  - 模型训练
  - 模型测试
  - 性能评估
  - 参数调优
  - 数据标注
  - 样本收集
  - 验证结果
  - 异常检测
  - 模式识别
  - 分类识别 